#!/usr/bin/env python3
"""
CogBridges Reddit用户画像分析 - 启动脚本
"""
import os
import sys
import argparse
import logging
import socket
from pathlib import Path

def setup_logging():
    """设置日志，强制使用UTF-8编码以避免Windows控制台的gbk编码错误"""
    # 尝试将标准输出/错误流重编码为utf-8，适用于Python 3.7+
    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')  # type: ignore
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')  # type: ignore
    except Exception:
        # 旧版本或不支持时忽略
        pass

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('startup.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误：需要Python 3.8或更高版本")
        print(f"当前版本：{sys.version}")
        sys.exit(1)
    print(f"✅ Python版本检查通过：{sys.version.split()[0]}")

def check_port_available(host, port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            if result == 0:
                return False  # 端口被占用
            return True  # 端口可用
    except Exception:
        return True  # 假设可用

def check_dependencies():
    """检查依赖"""
    try:
        import flask
        import praw
        import asyncpraw
        import aiohttp
        print("✅ 核心依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 依赖检查失败：{e}")
        print("请运行：pip install -r requirements.txt")
        return False

def check_config():
    """检查配置"""
    try:
        from config import config
        validation = config.validate_config()
        
        if validation["valid"]:
            print("✅ 配置验证通过")
            
            # 显示配置摘要
            summary = config.get_config_summary()
            print(f"   - AI服务：{summary['ai_service']}")
            print(f"   - LLM模型：{summary['llm_model']}")
            print(f"   - Reddit配置：{'已配置' if summary['reddit_configured'] else '未配置（只读模式）'}")
            print(f"   - 服务器：{summary['server']}")
            
            return True
        else:
            print("❌ 配置验证失败：")
            for error in validation["errors"]:
                print(f"   - {error}")
            return False
    except Exception as e:
        print(f"❌ 配置检查失败：{e}")
        return False

def create_directories():
    """创建必要的目录"""
    directories = [
        "data",
        "data/faiss_index", 
        "data/embeddings_cache",
        "detailed_results",
        "logs",
        "templates",
        "static/css",
        "static/js"
    ]
    
    for directory in directories:
        path = Path(directory)
        # 如果路径已存在且是文件，则跳过
        if path.exists() and path.is_file():
            continue
        # 创建目录
        path.mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构检查完成")

def check_environment():
    """检查环境文件"""
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  .env文件不存在，使用默认配置")
        print("   建议创建.env文件并配置API密钥")
        return True
    else:
        print("✅ .env文件存在")
        return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CogBridges Reddit用户画像分析启动器')
    parser.add_argument('--host', default='127.0.0.1', help='主机地址')
    parser.add_argument('--port', type=int, default=5000, help='端口号')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    parser.add_argument('--check-only', action='store_true', help='仅检查环境，不启动应用')
    
    args = parser.parse_args()
    
    logger = setup_logging()
    
    print("🚀 CogBridges Reddit用户画像分析启动器")
    print("=" * 50)
    
    # 环境检查
    print("\n📋 环境检查：")
    
    check_python_version()
    
    if not check_dependencies():
        sys.exit(1)
    
    create_directories()
    
    if not check_environment():
        sys.exit(1)
    
    if not check_config():
        sys.exit(1)
    
    print("\n✨ 环境检查完成！")
    
    if args.check_only:
        print("\n✅ 环境检查模式，不启动应用")
        return
    
    # 检查端口是否可用
    if not check_port_available(args.host, args.port):
        print(f"\n❌ 端口 {args.port} 已被占用！")
        print(f"请检查是否已有应用在运行，或使用其他端口：")
        print(f"python start.py --port 5001")
        sys.exit(1)

    # 启动应用
    print("\n🌟 启动应用...")
    try:
        # 添加only_profile和resona目录到Python路径
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
        only_profile_dir = os.path.join(project_root, 'only_profile')
        resona_dir = os.path.join(project_root, 'resona')
        sys.path.insert(0, only_profile_dir)
        sys.path.insert(0, resona_dir)
        sys.path.insert(0, project_root)

        from app import RedditProfileApp

        app = RedditProfileApp()
        
        print(f"\n🎯 应用启动中...")
        print(f"   - 地址：http://{args.host}:{args.port}")
        print(f"   - 调试模式：{'开启' if args.debug else '关闭'}")
        print(f"   - 按 Ctrl+C 停止应用")
        print("=" * 50)
        
        app.run(host=args.host, port=args.port, debug=args.debug)
        
    except KeyboardInterrupt:
        print("\n\n👋 应用已停止")
    except Exception as e:
        logger.error(f"启动失败：{e}")
        print(f"\n❌ 启动失败：{e}")
        sys.exit(1)

if __name__ == '__main__':
    main() 